import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';
import '../providers/quiz_provider.dart';

class QuizResultView extends StatelessWidget {
  final QuizProvider quizProvider;

  const QuizResultView({
    super.key,
    required this.quizProvider,
  });

  @override
  Widget build(BuildContext context) {
    final scorePercentage = (quizProvider.score / quizProvider.totalQuestions) * 100;
    
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Success icon
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: _getScoreColor(scorePercentage).withValues(alpha: 0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(
              _getScoreIcon(scorePercentage),
              size: 64,
              color: _getScoreColor(scorePercentage),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Congratulations text
          Text(
            'آزمون تکمیل شد!',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: _getScoreColor(scorePercentage),
            ),
            textAlign: TextAlign.center,
            textDirection: TextDirection.rtl,
          ),
          
          const SizedBox(height: 16),
          
          // Score text
          Text(
            'شما به ${quizProvider.score} سوال از ${quizProvider.totalQuestions} سوال پاسخ صحیح دادید',
            style: Theme.of(context).textTheme.titleLarge,
            textAlign: TextAlign.center,
            textDirection: TextDirection.rtl,
          ),
          
          const SizedBox(height: 24),
          
          // Score percentage with progress indicator
          _buildScoreIndicator(context, scorePercentage),
          
          const SizedBox(height: 24),
          
          // Motivational message
          _buildMotivationalMessage(context, scorePercentage),
          
          const SizedBox(height: 32),
          
          // Action buttons
          _buildActionButtons(context),
        ],
      ),
    );
  }

  Widget _buildScoreIndicator(BuildContext context, double scorePercentage) {
    return Column(
      children: [
        Text(
          '${scorePercentage.round()}%',
          style: Theme.of(context).textTheme.displayMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: _getScoreColor(scorePercentage),
          ),
        ),
        const SizedBox(height: 16),
        Container(
          width: double.infinity,
          height: 12,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
            color: Colors.grey[700],
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: scorePercentage / 100,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                color: _getScoreColor(scorePercentage),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMotivationalMessage(BuildContext context, double scorePercentage) {
    String message;
    if (scorePercentage >= 90) {
      message = 'عالی! شما عملکرد فوق‌العاده‌ای داشتید!';
    } else if (scorePercentage >= 70) {
      message = 'خوب! شما به خوبی مطالب را فراگرفته‌اید.';
    } else if (scorePercentage >= 50) {
      message = 'متوسط. با مطالعه بیشتر می‌توانید بهتر شوید.';
    } else {
      message = 'نیاز به مطالعه بیشتر دارید. دوباره تلاش کنید!';
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getScoreColor(scorePercentage).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getScoreColor(scorePercentage).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Text(
        message,
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.center,
        textDirection: TextDirection.rtl,
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        // Retry quiz button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () => quizProvider.resetQuiz(),
            icon: const Icon(Icons.refresh),
            label: const Text(
              'تکرار آزمون',
              style: TextStyle(
                fontFamily: 'Samim',
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.kPrimaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 12),
        
        // Back to lesson button
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () {
              // This will switch back to the content tab
              DefaultTabController.of(context).animateTo(0);
            },
            icon: const Icon(Icons.arrow_back),
            label: const Text(
              'بازگشت به درس',
              style: TextStyle(
                fontFamily: 'Samim',
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppTheme.kTextColor,
              side: BorderSide(color: AppTheme.kTextColor.withValues(alpha: 0.5)),
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Color _getScoreColor(double scorePercentage) {
    if (scorePercentage >= 70) {
      return AppTheme.kSuccessColor;
    } else if (scorePercentage >= 50) {
      return AppTheme.kLockedColor;
    } else {
      return Colors.red[400]!;
    }
  }

  IconData _getScoreIcon(double scorePercentage) {
    if (scorePercentage >= 90) {
      return Icons.emoji_events; // Trophy
    } else if (scorePercentage >= 70) {
      return Icons.thumb_up; // Thumbs up
    } else if (scorePercentage >= 50) {
      return Icons.sentiment_neutral; // Neutral face
    } else {
      return Icons.sentiment_dissatisfied; // Sad face
    }
  }
}
