import 'package:flutter/material.dart';
import '../models/lesson_model.dart';
import '../../lesson_detail/lesson_detail_screen.dart';

class LessonListItem extends StatelessWidget {
  final Lesson lesson;
  final VoidCallback? onTap;

  const LessonListItem({
    super.key,
    required this.lesson,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Card(
        color: Colors.grey[900],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        elevation: 4,
        child: ListTile(
          enabled: lesson.isFree,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 12,
          ),
          leading: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: lesson.isFree
                  ? Colors.tealAccent[400]?.withValues(alpha: 0.2)
                  : Colors.orange.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              lesson.isFree ? Icons.play_circle_fill : Icons.lock,
              color: lesson.isFree ? Colors.tealAccent[400] : Colors.orange,
              size: 28,
            ),
          ),
          title: Text(
            lesson.title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: lesson.isFree ? Colors.white : Colors.white70,
                  fontWeight: FontWeight.w600,
                ),
            textDirection: TextDirection.rtl,
            textAlign: TextAlign.right,
          ),
          subtitle: Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Icon(
                  lesson.isFree ? Icons.check_circle : Icons.star,
                  size: 16,
                  color: lesson.isFree ? Colors.green : Colors.amber,
                ),
                const SizedBox(width: 4),
                Text(
                  lesson.isFree ? 'درس رایگان' : 'درس ویژه',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: lesson.isFree ? Colors.green : Colors.amber,
                        fontWeight: FontWeight.w500,
                      ),
                  textDirection: TextDirection.rtl,
                ),
              ],
            ),
          ),
          trailing: lesson.isFree
              ? Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.tealAccent[400],
                  size: 16,
                )
              : Icon(
                  Icons.lock_outline,
                  color: Colors.orange.withValues(alpha: 0.7),
                  size: 16,
                ),
          onTap: lesson.isFree
              ? () => _navigateToLessonDetail(context)
              : () => _showLockedLessonDialog(context),
        ),
      ),
    );
  }

  void _navigateToLessonDetail(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => LessonDetailScreen(lessonId: lesson.id),
      ),
    );
  }

  void _showLockedLessonDialog(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'برای دسترسی به این درس، ابتدا باید اشتراک تهیه کنید',
          style: const TextStyle(fontFamily: 'Samim'),
          textDirection: TextDirection.rtl,
        ),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
