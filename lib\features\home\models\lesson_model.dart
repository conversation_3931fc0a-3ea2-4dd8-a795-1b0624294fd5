class Lesson {
  final int id;
  final String title;
  final bool isFree;

  Lesson({
    required this.id,
    required this.title,
    required this.isFree,
  });

  factory Lesson.fromJson(Map<String, dynamic> json) {
    return Lesson(
      id: json['id'] as int? ?? 0,
      title: json['title'] as String? ?? '',
      isFree: json['is_free'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'is_free': isFree,
    };
  }

  @override
  String toString() {
    return 'Lesson{id: $id, title: $title, isFree: $isFree}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Lesson &&
        other.id == id &&
        other.title == title &&
        other.isFree == isFree;
  }

  @override
  int get hashCode {
    return id.hashCode ^ title.hashCode ^ isFree.hashCode;
  }
}
