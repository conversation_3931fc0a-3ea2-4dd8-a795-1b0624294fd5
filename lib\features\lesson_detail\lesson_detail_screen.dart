import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/theme/app_theme.dart';
import 'providers/lesson_detail_provider.dart';

class LessonDetailScreen extends StatefulWidget {
  final int lessonId;

  const LessonDetailScreen({
    super.key,
    required this.lessonId,
  });

  @override
  State<LessonDetailScreen> createState() => _LessonDetailScreenState();
}

class _LessonDetailScreenState extends State<LessonDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Load lesson detail after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<LessonDetailProvider>(context, listen: false)
          .loadLessonDetail(widget.lessonId);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LessonDetailProvider>(
      builder: (context, provider, child) {
        return DefaultTabController(
          length: 3,
          child: Scaffold(
            extendBodyBehindAppBar: true,
            appBar: AppBar(
              title: Text(
                provider.hasLesson ? provider.lesson!.title : 'جزئیات درس',
                style: const TextStyle(
                  fontFamily: 'Samim',
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textDirection: TextDirection.rtl,
              ),
              backgroundColor: Colors.transparent,
              elevation: 0,
              flexibleSpace: Container(
                decoration: BoxDecoration(
                  gradient: AppGradients.appBarBackground,
                ),
              ),
              bottom: provider.hasLesson
                  ? TabBar(
                      controller: _tabController,
                      indicatorColor: Colors.white,
                      indicatorWeight: 3,
                      labelColor: Colors.white,
                      unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
                      labelStyle: const TextStyle(
                        fontFamily: 'Samim',
                        fontWeight: FontWeight.w600,
                      ),
                      tabs: const [
                        Tab(
                          text: 'محتوای درس',
                          icon: Icon(Icons.article),
                        ),
                        Tab(
                          text: 'آزمون',
                          icon: Icon(Icons.quiz),
                        ),
                        Tab(
                          text: 'تمرین نوشتن',
                          icon: Icon(Icons.edit_note),
                        ),
                      ],
                    )
                  : null,
            ),
            body: Container(
              decoration: const BoxDecoration(
                gradient: AppGradients.mainBackground,
              ),
              child: SafeArea(
                child: _buildBody(provider),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBody(LessonDetailProvider provider) {
    if (provider.isLoading) {
      return _buildLoadingState();
    }

    if (provider.hasError) {
      return _buildErrorState(provider);
    }

    if (!provider.hasLesson) {
      return _buildEmptyState();
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildContentTab(provider.lesson!),
        _buildQuizTab(),
        _buildExerciseTab(),
      ],
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: Colors.tealAccent[400],
          ),
          const SizedBox(height: 16),
          Text(
            'در حال بارگذاری جزئیات درس...',
            style: Theme.of(context).textTheme.bodyLarge,
            textDirection: TextDirection.rtl,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(LessonDetailProvider provider) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              'خطا در بارگذاری جزئیات درس',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
            ),
            const SizedBox(height: 8),
            Text(
              provider.error ?? 'خطای نامشخص',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => provider.retryLoadLessonDetail(widget.lessonId),
              icon: const Icon(Icons.refresh),
              label: const Text(
                'تلاش مجدد',
                style: TextStyle(fontFamily: 'Samim'),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.tealAccent[400],
                foregroundColor: Colors.black,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.school_outlined,
              size: 64,
              color: Colors.grey[600],
            ),
            const SizedBox(height: 16),
            Text(
              'درس یافت نشد',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
            ),
            const SizedBox(height: 8),
            Text(
              'اطلاعات این درس در دسترس نیست',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentTab(lesson) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Lesson title
          Text(
            lesson.title,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.tealAccent[400],
                ),
            textDirection: TextDirection.rtl,
            textAlign: TextAlign.right,
          ),
          const SizedBox(height: 24),

          // Lesson content
          SelectableText(
            lesson.content.isNotEmpty
                ? lesson.content
                : 'محتوای این درس هنوز اضافه نشده است.',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  height: 1.8,
                  fontSize: 16,
                ),
            textDirection: TextDirection.rtl,
            textAlign: TextAlign.justify,
          ),
        ],
      ),
    );
  }

  Widget _buildQuizTab() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.quiz,
              size: 64,
              color: Colors.tealAccent[400],
            ),
            const SizedBox(height: 16),
            Text(
              'آزمون این درس',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
            ),
            const SizedBox(height: 8),
            Text(
              'آزمون این درس به زودی اضافه می‌شود',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExerciseTab() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.edit_note,
              size: 64,
              color: Colors.tealAccent[400],
            ),
            const SizedBox(height: 16),
            Text(
              'تمرین نوشتن',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
            ),
            const SizedBox(height: 8),
            Text(
              'بخش تمرین نوشتن و بازخورد هوش مصنوعی به زودی اضافه می‌شود',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
            ),
          ],
        ),
      ),
    );
  }
}
