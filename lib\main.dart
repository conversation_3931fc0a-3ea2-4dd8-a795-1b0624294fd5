import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'core/theme/app_theme.dart';
import 'core/services/api_service.dart';
import 'features/home/<USER>';
import 'features/home/<USER>/lesson_provider.dart';
import 'features/lesson_detail/providers/lesson_detail_provider.dart';
import 'features/lesson_detail/providers/quiz_provider.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<LessonProvider>(
          create: (context) => LessonProvider(ApiService()),
        ),
        ChangeNotifierProvider<LessonDetailProvider>(
          create: (context) => LessonDetailProvider(ApiService()),
        ),
        ChangeNotifierProvider<QuizProvider>(
          create: (context) => QuizProvider(ApiService()),
        ),
      ],
      child: MaterialApp(
        title: 'نویسنده شو',
        theme: AppTheme.darkTheme(),
        debugShowCheckedModeBanner: false,
        locale: const Locale('fa', 'IR'),
        supportedLocales: const [
          Locale('fa', 'IR'),
        ],
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        home: const HomeScreen(),
      ),
    );
  }
}
